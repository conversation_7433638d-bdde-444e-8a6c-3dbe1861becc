import React, { useState, useEffect, useCallback } from 'react';
import DocumentViewer from '../common/DocumentViewer';

const ComprehensiveTest = () => {
  const [logs, setLogs] = useState([]);
  const [testResults, setTestResults] = useState({});

  // Capture all console logs
  useEffect(() => {
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    
    const addLog = (type, args) => {
      const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');
      
      setLogs(prev => [...prev, { 
        type, 
        message, 
        timestamp: new Date().toLocaleTimeString() 
      }]);
    };
    
    console.log = (...args) => {
      addLog('log', args);
      originalLog.apply(console, args);
    };
    
    console.error = (...args) => {
      addLog('error', args);
      originalError.apply(console, args);
    };
    
    console.warn = (...args) => {
      addLog('warn', args);
      originalWarn.apply(console, args);
    };
    
    return () => {
      console.log = originalLog;
      console.error = originalError;
      console.warn = originalWarn;
    };
  }, []);

  // Test the exact filename extraction logic
  const testFilenameExtraction = useCallback(() => {
    const content = {
      previewUrl: "https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3MUHQ5BOLEJMUNN5%2F20250716%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250716T051917Z&X-Amz-Expires=86400&X-Amz-Signature=0bdfa5e180e830073dc84103fcd67859485f04c4c2916c6eb8a4f1b9f3a782d1&X-Amz-SignedHeaders=host",
      fileUrl: null,
      accessibleFileUrl: "https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3MUHQ5BOLEJMUNN5%2F20250716%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250716T051917Z&X-Amz-Expires=86400&X-Amz-Signature=0bdfa5e180e830073dc84103fcd67859485f04c4c2916c6eb8a4f1b9f3a782d1&X-Amz-SignedHeaders=host"
    };

    console.log('[TEST] Starting filename extraction test...');
    console.log('[TEST] Content data:', content);

    // Step 1: Extract URL parts
    const previewUrlName = content.previewUrl?.split("/").pop();
    console.log('[TEST] previewUrlName (with query):', previewUrlName);

    const previewUrlNameClean = content.previewUrl?.split("?")[0].split("/").pop();
    console.log('[TEST] previewUrlNameClean (without query):', previewUrlNameClean);

    // Step 2: Test regex match
    const regexPattern = /^\d+-(.+)_preview\.pdf$/;
    const match = previewUrlNameClean?.match(regexPattern);
    console.log('[TEST] Regex pattern:', regexPattern.toString());
    console.log('[TEST] Regex match result:', match);

    let extractedFileName;
    if (match) {
      const originalPart = match[1];
      extractedFileName = originalPart + ".pdf";
      console.log('[TEST] Extracted original part:', originalPart);
      console.log('[TEST] Final filename:', extractedFileName);
    } else {
      extractedFileName = previewUrlNameClean?.replace("_preview", "") || "document.pdf";
      console.log('[TEST] Fallback filename:', extractedFileName);
    }

    return {
      previewUrlName,
      previewUrlNameClean,
      regexMatch: match,
      extractedFileName,
      fileUrl: content.previewUrl
    };
  }, []);

  // Test document type detection
  const testDocumentTypeDetection = useCallback(async (fileName, fileUrl) => {
    console.log('[TEST] Testing document type detection...');
    console.log('[TEST] Input fileName:', fileName);
    console.log('[TEST] Input fileUrl:', fileUrl);

    // Copy the exact logic from DocumentViewer
    const getFileExtension = (filenameOrUrl) => {
      if (!filenameOrUrl) return '';

      let source = filenameOrUrl;

      if (filenameOrUrl.includes('/')) {
        const urlPath = filenameOrUrl.split('?')[0];
        source = urlPath.substring(urlPath.lastIndexOf('/') + 1);
      }

      const lastDotIndex = source.lastIndexOf('.');
      if (lastDotIndex === -1) return '';

      const extension = source.substring(lastDotIndex).toLowerCase();
      console.log('[TEST] Extension detection:', { filenameOrUrl, source, extension });
      return extension;
    };

    const getDocumentType = async (filename, previewUrl = null) => {
      console.log('[TEST] Document type detection:', { filename, previewUrl });

      let extension = getFileExtension(filename);
      console.log('[TEST] Extension from filename:', extension);

      if (!extension && previewUrl) {
        extension = getFileExtension(previewUrl);
        console.log('[TEST] Extension from previewUrl:', extension);
      }

      if (extension === '.pdf') {
        console.log('[TEST] Detected as PDF via extension');
        return 'pdf';
      }

      if (previewUrl && (previewUrl.includes('preview.pdf') || previewUrl.includes('_preview.pdf'))) {
        console.log('[TEST] Detected as PDF via preview URL pattern');
        return 'pdf';
      }

      if (filename && filename.toLowerCase().includes('pdf')) {
        console.log('[TEST] Detected as PDF via filename pattern');
        return 'pdf';
      }

      if (previewUrl && previewUrl.toLowerCase().includes('pdf')) {
        console.log('[TEST] Detected as PDF via URL pattern');
        return 'pdf';
      }

      console.log('[TEST] Could not detect as PDF, marking as unknown');
      return 'unknown';
    };

    const detectedType = await getDocumentType(fileName, fileUrl);
    console.log('[TEST] Final detected type:', detectedType);

    return {
      filenameExtension: getFileExtension(fileName),
      urlExtension: getFileExtension(fileUrl),
      detectedType,
      urlContainsPreviewPdf: fileUrl?.includes('preview.pdf'),
      urlContainsUnderscorePreviewPdf: fileUrl?.includes('_preview.pdf'),
      filenameContainsPdf: fileName?.toLowerCase().includes('pdf'),
      urlContainsPdf: fileUrl?.toLowerCase().includes('pdf')
    };
  }, []);

  // Run comprehensive test
  const runComprehensiveTest = useCallback(async () => {
    console.log('[TEST] ========== STARTING COMPREHENSIVE TEST ==========');
    
    const filenameTest = testFilenameExtraction();
    const detectionTest = await testDocumentTypeDetection(filenameTest.extractedFileName, filenameTest.fileUrl);
    
    setTestResults({
      filenameExtraction: filenameTest,
      documentTypeDetection: detectionTest,
      finalResult: detectionTest.detectedType === 'pdf' ? 'SUCCESS' : 'FAILURE'
    });
    
    console.log('[TEST] ========== TEST COMPLETED ==========');
  }, [testFilenameExtraction, testDocumentTypeDetection]);

  useEffect(() => {
    runComprehensiveTest();
  }, [runComprehensiveTest]);

  return (
    <div style={{ padding: '20px', maxWidth: '1400px', margin: '0 auto' }}>
      <h1>Comprehensive PDF Detection Test</h1>
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
        {/* Test Results */}
        <div style={{ backgroundColor: '#f8f9fa', padding: '20px', borderRadius: '8px' }}>
          <h2>Test Results</h2>
          {Object.keys(testResults).length > 0 && (
            <div>
              <div style={{ 
                padding: '10px', 
                backgroundColor: testResults.finalResult === 'SUCCESS' ? '#d4edda' : '#f8d7da',
                borderRadius: '4px',
                marginBottom: '15px',
                fontWeight: 'bold'
              }}>
                Final Result: {testResults.finalResult}
              </div>
              
              <details style={{ marginBottom: '10px' }}>
                <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>Filename Extraction</summary>
                <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                  {JSON.stringify(testResults.filenameExtraction, null, 2)}
                </pre>
              </details>
              
              <details>
                <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>Document Type Detection</summary>
                <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                  {JSON.stringify(testResults.documentTypeDetection, null, 2)}
                </pre>
              </details>
            </div>
          )}
        </div>

        {/* Console Logs */}
        <div style={{ backgroundColor: '#f8f9fa', padding: '20px', borderRadius: '8px' }}>
          <h2>Console Logs</h2>
          <button 
            onClick={() => setLogs([])}
            style={{
              padding: '5px 10px',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginBottom: '10px'
            }}
          >
            Clear Logs
          </button>
          <div style={{ 
            maxHeight: '400px', 
            overflowY: 'auto',
            backgroundColor: '#000',
            color: '#fff',
            padding: '10px',
            borderRadius: '4px',
            fontFamily: 'monospace',
            fontSize: '11px'
          }}>
            {logs.map((log, index) => (
              <div key={index} style={{ 
                color: log.type === 'error' ? '#ff6b6b' : log.type === 'warn' ? '#ffd93d' : '#fff',
                marginBottom: '2px',
                wordBreak: 'break-all'
              }}>
                [{log.timestamp}] {log.message}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Live DocumentViewer Test */}
      <div style={{ backgroundColor: '#fff', border: '2px solid #007bff', padding: '20px', borderRadius: '8px' }}>
        <h2>Live DocumentViewer Test</h2>
        {testResults.filenameExtraction && (
          <DocumentViewer
            fileUrl={testResults.filenameExtraction.fileUrl}
            fileName={testResults.filenameExtraction.extractedFileName}
            title="Comprehensive Test Document"
            height="500px"
            showDownload={false}
          />
        )}
      </div>
    </div>
  );
};

export default ComprehensiveTest;
