import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const FinalDiagnostic = () => {
  const [diagnosticResults, setDiagnosticResults] = useState({});
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { message, type, timestamp }]);
    console.log(`[FinalDiagnostic] ${message}`);
  };

  // Test 1: PDF.js Worker Configuration
  const testWorkerConfiguration = () => {
    addLog('Testing PDF.js worker configuration...');
    addLog(`PDF.js version: ${pdfjs.version}`);
    addLog(`Worker source: ${pdfjs.GlobalWorkerOptions.workerSrc}`);
    
    return {
      version: pdfjs.version,
      workerSrc: pdfjs.GlobalWorkerOptions.workerSrc,
      status: 'configured'
    };
  };

  // Test 2: Document Type Detection
  const testDocumentTypeDetection = () => {
    addLog('Testing document type detection...');
    
    const testCases = [
      {
        name: 'S3 URL with query params',
        fileUrl: 'https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256',
        fileName: 'Basketball-Coaching-Resource-Book.pdf'
      },
      {
        name: 'Simple PDF URL',
        fileUrl: 'https://example.com/document.pdf',
        fileName: 'document.pdf'
      }
    ];

    const results = testCases.map(testCase => {
      // Simulate the exact detection logic
      const getFileExtension = (filenameOrUrl) => {
        if (!filenameOrUrl) return '';
        let source = filenameOrUrl;
        if (filenameOrUrl.includes('/')) {
          const urlPath = filenameOrUrl.split('?')[0];
          source = urlPath.substring(urlPath.lastIndexOf('/') + 1);
        }
        const lastDotIndex = source.lastIndexOf('.');
        if (lastDotIndex === -1) return '';
        return source.substring(lastDotIndex).toLowerCase();
      };

      const filenameExt = getFileExtension(testCase.fileName);
      const urlExt = getFileExtension(testCase.fileUrl);
      const detected = filenameExt === '.pdf' || urlExt === '.pdf' || 
                     testCase.fileUrl.includes('preview.pdf') ||
                     testCase.fileName.toLowerCase().includes('pdf') ||
                     testCase.fileUrl.toLowerCase().includes('pdf');

      addLog(`${testCase.name}: ${detected ? 'DETECTED' : 'NOT DETECTED'}`);
      
      return {
        ...testCase,
        filenameExt,
        urlExt,
        detected
      };
    });

    return results;
  };

  // Test 3: Direct PDF Loading
  const DirectPDFTest = ({ url, name }) => {
    const [status, setStatus] = useState('loading');
    const [error, setError] = useState(null);

    return (
      <div style={{ 
        border: '1px solid #ddd', 
        padding: '10px', 
        margin: '10px 0',
        borderRadius: '4px'
      }}>
        <h4>{name}</h4>
        <div style={{ height: '200px', overflow: 'hidden' }}>
          <Document
            file={url}
            onLoadSuccess={() => {
              setStatus('success');
              addLog(`${name}: PDF loaded successfully`, 'success');
            }}
            onLoadError={(err) => {
              setStatus('error');
              setError(err.message);
              addLog(`${name}: PDF load failed - ${err.message}`, 'error');
            }}
            loading={<div>Loading {name}...</div>}
            error={<div style={{ color: 'red' }}>Failed to load {name}</div>}
          >
            <Page pageNumber={1} width={300} />
          </Document>
        </div>
        <div style={{ 
          marginTop: '5px', 
          fontSize: '12px',
          color: status === 'success' ? 'green' : status === 'error' ? 'red' : 'orange'
        }}>
          Status: {status} {error && `(${error})`}
        </div>
      </div>
    );
  };

  // Run all tests
  useEffect(() => {
    addLog('Starting comprehensive diagnostic...');
    
    const workerTest = testWorkerConfiguration();
    const detectionTest = testDocumentTypeDetection();
    
    setDiagnosticResults({
      worker: workerTest,
      detection: detectionTest,
      timestamp: new Date().toISOString()
    });
    
    addLog('Diagnostic tests completed');
  }, []);

  return (
    <div style={{ padding: '20px', maxWidth: '1400px', margin: '0 auto' }}>
      <h1>Final PDF Diagnostic</h1>
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
        {/* Diagnostic Results */}
        <div>
          <h2>Diagnostic Results</h2>
          <div style={{ backgroundColor: '#f8f9fa', padding: '15px', borderRadius: '8px' }}>
            {Object.keys(diagnosticResults).length > 0 && (
              <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                {JSON.stringify(diagnosticResults, null, 2)}
              </pre>
            )}
          </div>
        </div>

        {/* Live Logs */}
        <div>
          <h2>Live Logs</h2>
          <div style={{ 
            backgroundColor: '#000', 
            color: '#fff', 
            padding: '10px', 
            borderRadius: '4px',
            height: '300px',
            overflowY: 'auto',
            fontFamily: 'monospace',
            fontSize: '11px'
          }}>
            {logs.map((log, index) => (
              <div key={index} style={{ 
                color: log.type === 'error' ? '#ff6b6b' : 
                       log.type === 'success' ? '#51cf66' : '#fff',
                marginBottom: '2px'
              }}>
                [{log.timestamp}] {log.message}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Direct PDF Loading Tests */}
      <div style={{ marginTop: '20px' }}>
        <h2>Direct PDF Loading Tests</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '10px' }}>
          <DirectPDFTest 
            url="https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"
            name="Mozilla PDF (Known Working)"
          />
          <DirectPDFTest 
            url="https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
            name="W3C Dummy PDF"
          />
          <DirectPDFTest 
            url="https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3MUHQ5BOLEJMUNN5%2F20250716%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250716T051917Z&X-Amz-Expires=86400&X-Amz-Signature=0bdfa5e180e830073dc84103fcd67859485f04c4c2916c6eb8a4f1b9f3a782d1&X-Amz-SignedHeaders=host"
            name="Your S3 PDF"
          />
        </div>
      </div>

      {/* Summary */}
      <div style={{ 
        marginTop: '20px',
        padding: '15px',
        backgroundColor: '#e9ecef',
        borderRadius: '8px'
      }}>
        <h2>Diagnostic Summary</h2>
        <ul>
          <li><strong>If Mozilla PDF works:</strong> react-pdf is configured correctly</li>
          <li><strong>If S3 PDF fails with CORS error:</strong> S3 bucket needs CORS configuration</li>
          <li><strong>If S3 PDF fails with 403/404:</strong> Signed URL has expired or is invalid</li>
          <li><strong>If all PDFs fail:</strong> PDF.js worker or library issue</li>
          <li><strong>If detection tests fail:</strong> Document type detection logic issue</li>
        </ul>
      </div>
    </div>
  );
};

export default FinalDiagnostic;
