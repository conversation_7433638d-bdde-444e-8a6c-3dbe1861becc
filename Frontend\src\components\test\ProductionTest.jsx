import React from 'react';
import DocumentViewer from '../common/DocumentViewer';

const ProductionTest = () => {
  // Exact data from your API response
  const content = {
    "_id": "68772d5bf14ab32c29eb4924",
    "title": "Testing content store AWS S3",
    "description": "<p>Testing content store AWS S3</p>",
    "sport": "Basketball",
    "contentType": "Document",
    "previewUrl": "https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3MUHQ5BOLEJMUNN5%2F20250716%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250716T051917Z&X-Amz-Expires=86400&X-Amz-Signature=0bdfa5e180e830073dc84103fcd67859485f04c4c2916c6eb8a4f1b9f3a782d1&X-Amz-SignedHeaders=host",
    "previewStatus": "completed",
    "fileAccess": {
      "accessType": "preview",
      "canAccessFull": false,
      "hasPreview": true,
      "reason": "preview_only"
    },
    "accessibleFileUrl": "https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3MUHQ5BOLEJMUNN5%2F20250716%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250716T051917Z&X-Amz-Expires=86400&X-Amz-Signature=0bdfa5e180e830073dc84103fcd67859485f04c4c2916c6eb8a4f1b9f3a782d1&X-Amz-SignedHeaders=host"
  };

  // Simulate the exact filename extraction logic from BuyerContentDetail.jsx
  const getFileName = () => {
    const fileUrlName = content.fileUrl?.split("/").pop();
    const accessibleFileUrlName = content.accessibleFileUrl?.split("/").pop();
    const previewUrlName = content.previewUrl?.split("/").pop();

    // Use fileUrl if available (user has full access)
    if (fileUrlName && !fileUrlName.includes("_preview")) {
      return fileUrlName;
    }

    // Use accessibleFileUrl if available
    if (accessibleFileUrlName && !accessibleFileUrlName.includes("_preview")) {
      return accessibleFileUrlName;
    }

    // For PDF preview files, try to extract original filename
    if (previewUrlName && (previewUrlName.includes("preview.pdf") || previewUrlName.includes("_preview.pdf"))) {
      // Pattern: timestamp-originalname_preview.pdf
      // Extract the original filename part
      const match = previewUrlName.match(/^\d+-(.+)_preview\.pdf$/);
      if (match) {
        // Reconstruct the original filename as PDF
        const originalPart = match[1];
        return originalPart + ".pdf";
      }
    }

    // Use previewUrl and clean up the name
    if (previewUrlName) {
      // Remove _preview suffix if present
      return previewUrlName.replace("_preview", "");
    }

    // Default to PDF since we only support PDF documents
    return "document.pdf";
  };

  const getFileUrl = () => {
    return content.previewUrl.startsWith("/uploads") 
      ? "IMAGE_BASE_URL" + content.previewUrl 
      : content.previewUrl;
  };

  const fileName = getFileName();
  const fileUrl = getFileUrl();

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Production Test - Exact API Response Simulation</h1>
      <p>This test simulates the exact scenario from your API response with all the fixes applied.</p>
      
      {/* Content Info */}
      <div style={{ 
        backgroundColor: '#f8f9fa', 
        padding: '20px', 
        borderRadius: '8px', 
        marginBottom: '20px' 
      }}>
        <h2>Content Information</h2>
        <div style={{ display: 'grid', gridTemplateColumns: '200px 1fr', gap: '10px', fontSize: '14px' }}>
          <strong>Content ID:</strong> <span>{content._id}</span>
          <strong>Title:</strong> <span>{content.title}</span>
          <strong>Content Type:</strong> <span>{content.contentType}</span>
          <strong>Preview Status:</strong> <span>{content.previewStatus}</span>
          <strong>Access Type:</strong> <span>{content.fileAccess.accessType}</span>
          <strong>Has Preview:</strong> <span>{content.fileAccess.hasPreview ? 'Yes' : 'No'}</span>
        </div>
      </div>

      {/* Extracted Values */}
      <div style={{ 
        backgroundColor: '#e9ecef', 
        padding: '20px', 
        borderRadius: '8px', 
        marginBottom: '20px' 
      }}>
        <h2>Extracted Values</h2>
        <div style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          <div><strong>Extracted fileName:</strong> {fileName}</div>
          <div><strong>File URL:</strong> {fileUrl}</div>
          <div><strong>Original previewUrl:</strong> {content.previewUrl}</div>
        </div>
      </div>

      {/* Expected Behavior */}
      <div style={{ 
        backgroundColor: '#d4edda', 
        border: '1px solid #c3e6cb', 
        padding: '15px', 
        borderRadius: '4px',
        marginBottom: '20px'
      }}>
        <h3>✅ Expected Behavior (After Fixes):</h3>
        <ul>
          <li>Document type should be detected as 'pdf'</li>
          <li>UniversalPDFViewer should be rendered</li>
          <li>PDF should load and display content</li>
          <li>If react-pdf fails, iframe fallback should work</li>
          <li>No "Preview not available" message should appear</li>
        </ul>
      </div>

      {/* DocumentViewer Test */}
      <div style={{ 
        border: '3px solid #28a745', 
        padding: '20px', 
        borderRadius: '8px',
        backgroundColor: '#fff'
      }}>
        <h2>🎯 Production DocumentViewer Test</h2>
        <p>This is the exact same call that would be made in BuyerContentDetail.jsx:</p>
        
        <DocumentViewer
          fileUrl={fileUrl}
          fileName={fileName}
          title="Document Preview"
          className="ItemDetail__documentPreview"
          height="600px"
          showDownload={false}
        />
      </div>

      {/* Troubleshooting */}
      <div style={{ 
        backgroundColor: '#fff3cd', 
        border: '1px solid #ffeaa7', 
        padding: '15px', 
        borderRadius: '4px',
        marginTop: '20px'
      }}>
        <h3>🔧 If Still Not Working:</h3>
        <ol>
          <li><strong>Check browser console</strong> for [DocumentViewer] and [UniversalPDFViewer] logs</li>
          <li><strong>CORS Issue:</strong> If you see CORS errors, S3 bucket needs CORS configuration</li>
          <li><strong>Expired URL:</strong> If you see 403/404 errors, the signed URL has expired</li>
          <li><strong>PDF.js Issue:</strong> If react-pdf fails, check if iframe fallback works</li>
          <li><strong>Network Issue:</strong> Try opening the PDF URL directly in a new tab</li>
        </ol>
      </div>
    </div>
  );
};

export default ProductionTest;
