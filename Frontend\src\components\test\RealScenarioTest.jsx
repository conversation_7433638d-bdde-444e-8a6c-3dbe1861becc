import React, { useState, useEffect } from 'react';
import DocumentViewer from '../common/DocumentViewer';

const RealScenarioTest = () => {
  const [debugInfo, setDebugInfo] = useState({});

  // Simulate the exact data from your API response
  const content = {
    previewUrl: "https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3MUHQ5BOLEJMUNN5%2F20250716%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250716T051917Z&X-Amz-Expires=86400&X-Amz-Signature=0bdfa5e180e830073dc84103fcd67859485f04c4c2916c6eb8a4f1b9f3a782d1&X-Amz-SignedHeaders=host",
    fileUrl: null,
    accessibleFileUrl: "https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3MUHQ5BOLEJMUNN5%2F20250716%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250716T051917Z&X-Amz-Expires=86400&X-Amz-Signature=0bdfa5e180e830073dc84103fcd67859485f04c4c2916c6eb8a4f1b9f3a782d1&X-Amz-SignedHeaders=host"
  };

  // Simulate the exact filename extraction logic from BuyerContentDetail.jsx
  const getFileName = () => {
    // Try to get filename from available URLs
    // Priority: fileUrl > accessibleFileUrl > previewUrl
    const fileUrlName = content.fileUrl?.split("/").pop();
    const accessibleFileUrlName = content.accessibleFileUrl?.split("/").pop();
    const previewUrlName = content.previewUrl?.split("/").pop();

    // Use fileUrl if available (user has full access)
    if (fileUrlName && !fileUrlName.includes("_preview")) {
      return fileUrlName;
    }

    // Use accessibleFileUrl if available
    if (accessibleFileUrlName && !accessibleFileUrlName.includes("_preview")) {
      return accessibleFileUrlName;
    }

    // For PDF preview files, try to extract original filename
    if (previewUrlName && (previewUrlName.includes("preview.pdf") || previewUrlName.includes("_preview.pdf"))) {
      // Pattern: timestamp-originalname_preview.pdf
      // Extract the original filename part
      const match = previewUrlName.match(/^\d+-(.+)_preview\.pdf$/);
      if (match) {
        // Reconstruct the original filename as PDF
        const originalPart = match[1];
        return originalPart + ".pdf";
      }
    }

    // Use previewUrl and clean up the name
    if (previewUrlName) {
      // Remove _preview suffix if present
      return previewUrlName.replace("_preview", "");
    }

    // Default to PDF since we only support PDF documents
    return "document.pdf";
  };

  const getFileUrl = () => {
    return content.previewUrl.startsWith("/uploads") 
      ? "IMAGE_BASE_URL" + content.previewUrl 
      : content.previewUrl;
  };

  useEffect(() => {
    const fileName = getFileName();
    const fileUrl = getFileUrl();
    const previewUrlName = content.previewUrl?.split("/").pop();
    const previewUrlNameWithoutQuery = content.previewUrl?.split("?")[0].split("/").pop();

    setDebugInfo({
      originalPreviewUrl: content.previewUrl,
      extractedFileName: fileName,
      extractedFileUrl: fileUrl,
      previewUrlName,
      previewUrlNameWithoutQuery,
      fileUrlName: content.fileUrl?.split("/").pop() || 'null',
      accessibleFileUrlName: content.accessibleFileUrl?.split("/").pop() || 'null',
      regexMatch: previewUrlNameWithoutQuery?.match(/^\d+-(.+)_preview\.pdf$/),
    });
  }, []);

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Real Scenario Test</h1>
      <p>This simulates the exact scenario from BuyerContentDetail.jsx with your API data.</p>
      
      <div style={{ backgroundColor: '#f8f9fa', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>
        <h2>Debug Information</h2>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <tbody>
            {Object.entries(debugInfo).map(([key, value]) => (
              <tr key={key}>
                <td style={{ 
                  border: '1px solid #ddd', 
                  padding: '8px', 
                  fontWeight: 'bold',
                  backgroundColor: '#e9ecef',
                  width: '30%'
                }}>
                  {key}
                </td>
                <td style={{ 
                  border: '1px solid #ddd', 
                  padding: '8px',
                  wordBreak: 'break-all',
                  fontFamily: 'monospace',
                  fontSize: '12px'
                }}>
                  {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <h2>DocumentViewer Test</h2>
      <p>This should show the PDF preview if everything is working correctly:</p>
      
      <div style={{ border: '2px solid #007bff', padding: '20px', borderRadius: '8px' }}>
        <DocumentViewer
          fileUrl={getFileUrl()}
          fileName={getFileName()}
          title="Document Preview"
          className="ItemDetail__documentPreview"
          height="500px"
          showDownload={false}
        />
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#fff3cd', border: '1px solid #ffeaa7', borderRadius: '4px' }}>
        <h3>Expected Behavior:</h3>
        <ul>
          <li>✅ fileName should be: "Basketball-Coaching-Resource-Book.pdf"</li>
          <li>✅ fileUrl should contain the S3 URL</li>
          <li>✅ DocumentViewer should detect this as a PDF</li>
          <li>✅ PDF should render in the viewer</li>
        </ul>
        <h3>If you see "Only PDF documents are supported for preview":</h3>
        <ul>
          <li>❌ The document type detection is failing</li>
          <li>❌ Check the browser console for [DocumentViewer] logs</li>
        </ul>
      </div>
    </div>
  );
};

export default RealScenarioTest;
