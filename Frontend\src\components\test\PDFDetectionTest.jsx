import React, { useState, useCallback, useEffect } from 'react';
import DocumentViewer from '../common/DocumentViewer';

const PDFDetectionTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [debugLogs, setDebugLogs] = useState([]);

  // Capture console logs
  useEffect(() => {
    const originalLog = console.log;
    const originalError = console.error;

    console.log = (...args) => {
      if (args[0] && args[0].includes && args[0].includes('[DocumentViewer]')) {
        setDebugLogs(prev => [...prev, { type: 'log', message: args.join(' '), timestamp: new Date().toLocaleTimeString() }]);
      }
      originalLog.apply(console, args);
    };

    console.error = (...args) => {
      setDebugLogs(prev => [...prev, { type: 'error', message: args.join(' '), timestamp: new Date().toLocaleTimeString() }]);
      originalError.apply(console, args);
    };

    return () => {
      console.log = originalLog;
      console.error = originalError;
    };
  }, []);

  // Test URLs based on your API response
  const testCases = [
    {
      name: "S3 PDF with query params",
      fileUrl: "https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3MUHQ5BOLEJMUNN5%2F20250716%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250716T051917Z&X-Amz-Expires=86400&X-Amz-Signature=0bdfa5e180e830073dc84103fcd67859485f04c4c2916c6eb8a4f1b9f3a782d1&X-Amz-SignedHeaders=host",
      fileName: "Basketball-Coaching-Resource-Book.pdf"
    },
    {
      name: "Simple PDF URL",
      fileUrl: "https://example.com/document.pdf",
      fileName: "document.pdf"
    },
    {
      name: "PDF with preview in name",
      fileUrl: "https://example.com/test_preview.pdf",
      fileName: "test_preview.pdf"
    },
    {
      name: "No extension in filename",
      fileUrl: "https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf",
      fileName: "Basketball-Coaching-Resource-Book"
    }
  ];

  // Get file extension function (copied from DocumentViewer for testing)
  const getFileExtension = useCallback((filenameOrUrl) => {
    if (!filenameOrUrl) return '';

    let source = filenameOrUrl;

    if (filenameOrUrl.includes('/')) {
      const urlPath = filenameOrUrl.split('?')[0];
      source = urlPath.substring(urlPath.lastIndexOf('/') + 1);
    }

    const lastDotIndex = source.lastIndexOf('.');
    if (lastDotIndex === -1) return '';

    const extension = source.substring(lastDotIndex).toLowerCase();
    return extension;
  }, []);

  // Document type detection function (copied from DocumentViewer for testing)
  const getDocumentType = useCallback(async (filename, previewUrl = null) => {
    let extension = getFileExtension(filename);

    if (!extension && previewUrl) {
      extension = getFileExtension(previewUrl);
    }

    if (extension === '.pdf') {
      return 'pdf';
    }

    if (previewUrl && (previewUrl.includes('preview.pdf') || previewUrl.includes('_preview.pdf'))) {
      return 'pdf';
    }

    if (filename && filename.toLowerCase().includes('pdf')) {
      return 'pdf';
    }

    if (previewUrl && previewUrl.toLowerCase().includes('pdf')) {
      return 'pdf';
    }

    return 'unknown';
  }, [getFileExtension]);

  const runTests = async () => {
    const results = [];

    for (const testCase of testCases) {
      const filenameExtension = getFileExtension(testCase.fileName);
      const urlExtension = getFileExtension(testCase.fileUrl);
      const detectedType = await getDocumentType(testCase.fileName, testCase.fileUrl);

      results.push({
        ...testCase,
        filenameExtension,
        urlExtension,
        detectedType,
        passed: detectedType === 'pdf'
      });
    }

    setTestResults(results);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>PDF Detection Test</h1>

      <button
        onClick={runTests}
        style={{
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          marginBottom: '20px'
        }}
      >
        Run Tests
      </button>

      {testResults.length > 0 && (
        <div>
          <h2>Test Results</h2>
          <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: '30px' }}>
            <thead>
              <tr style={{ backgroundColor: '#f8f9fa' }}>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Test Case</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Filename Ext</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>URL Ext</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Detected Type</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Result</th>
              </tr>
            </thead>
            <tbody>
              {testResults.map((result, index) => (
                <tr key={index}>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>{result.name}</td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>{result.filenameExtension || 'none'}</td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>{result.urlExtension || 'none'}</td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>{result.detectedType}</td>
                  <td style={{
                    border: '1px solid #ddd',
                    padding: '8px',
                    backgroundColor: result.passed ? '#d4edda' : '#f8d7da',
                    color: result.passed ? '#155724' : '#721c24'
                  }}>
                    {result.passed ? 'PASS' : 'FAIL'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {debugLogs.length > 0 && (
        <div>
          <h2>Debug Logs</h2>
          <div style={{
            backgroundColor: '#f8f9fa',
            border: '1px solid #ddd',
            padding: '10px',
            maxHeight: '200px',
            overflowY: 'auto',
            fontFamily: 'monospace',
            fontSize: '12px',
            marginBottom: '20px'
          }}>
            {debugLogs.map((log, index) => (
              <div key={index} style={{
                color: log.type === 'error' ? 'red' : 'black',
                marginBottom: '2px'
              }}>
                [{log.timestamp}] {log.message}
              </div>
            ))}
          </div>
          <button
            onClick={() => setDebugLogs([])}
            style={{
              padding: '5px 10px',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginBottom: '20px'
            }}
          >
            Clear Logs
          </button>
        </div>
      )}

      <h2>Live PDF Viewer Tests</h2>

      <h3>Test 1: Public PDF (should work)</h3>
      <div style={{ border: '1px solid #ddd', padding: '20px', marginTop: '20px' }}>
        <DocumentViewer
          fileUrl="https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
          fileName="dummy.pdf"
          title="Test PDF Document"
          height="400px"
        />
      </div>

      <h3>Test 2: S3 URL from your API response</h3>
      <p>Testing with the actual S3 URL (may fail due to CORS or expired signature):</p>
      <div style={{ border: '1px solid #ddd', padding: '20px', marginTop: '20px' }}>
        <DocumentViewer
          fileUrl="https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3MUHQ5BOLEJMUNN5%2F20250716%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250716T051917Z&X-Amz-Expires=86400&X-Amz-Signature=0bdfa5e180e830073dc84103fcd67859485f04c4c2916c6eb8a4f1b9f3a782d1&X-Amz-SignedHeaders=host"
          fileName="Basketball-Coaching-Resource-Book.pdf"
          title="S3 PDF Document"
          height="400px"
        />
      </div>

      <h3>Test 3: Another public PDF</h3>
      <div style={{ border: '1px solid #ddd', padding: '20px', marginTop: '20px' }}>
        <DocumentViewer
          fileUrl="https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"
          fileName="tracemonkey-pldi-09.pdf"
          title="Mozilla PDF.js Test Document"
          height="400px"
        />
      </div>
    </div>
  );
};

export default PDFDetectionTest;
