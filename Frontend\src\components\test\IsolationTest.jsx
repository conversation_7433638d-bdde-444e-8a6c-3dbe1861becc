import React, { useState } from 'react';
import DocumentViewer from '../common/DocumentViewer';

const IsolationTest = () => {
  const [selectedTest, setSelectedTest] = useState('working-pdf');

  const testCases = {
    'working-pdf': {
      name: 'Known Working PDF',
      fileUrl: 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
      fileName: 'tracemonkey-pldi-09.pdf',
      description: 'This should definitely work if react-pdf is configured correctly'
    },
    'simple-pdf': {
      name: 'Simple PDF',
      fileUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
      fileName: 'dummy.pdf',
      description: 'Another known working PDF'
    },
    's3-pdf': {
      name: 'Your S3 PDF',
      fileUrl: 'https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3MUHQ5BOLEJMUNN5%2F20250716%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250716T051917Z&X-Amz-Expires=86400&X-Amz-Signature=0bdfa5e180e830073dc84103fcd67859485f04c4c2916c6eb8a4f1b9f3a782d1&X-Amz-SignedHeaders=host',
      fileName: 'Basketball-Coaching-Resource-Book.pdf',
      description: 'Your actual S3 PDF - may fail due to CORS or expired signature'
    },
    'bad-extension': {
      name: 'Bad Extension Test',
      fileUrl: 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
      fileName: 'document.docx',
      description: 'Good PDF URL but wrong filename extension - should still work due to URL detection'
    },
    'no-extension': {
      name: 'No Extension Test',
      fileUrl: 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
      fileName: 'document',
      description: 'Good PDF URL but no extension - should work due to URL detection'
    }
  };

  const currentTest = testCases[selectedTest];

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>PDF Isolation Test</h1>
      <p>This test isolates different scenarios to identify the exact issue.</p>

      {/* Test Selection */}
      <div style={{ marginBottom: '20px' }}>
        <h2>Select Test Case:</h2>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
          {Object.entries(testCases).map(([key, test]) => (
            <button
              key={key}
              onClick={() => setSelectedTest(key)}
              style={{
                padding: '10px 15px',
                backgroundColor: selectedTest === key ? '#007bff' : '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {test.name}
            </button>
          ))}
        </div>
      </div>

      {/* Current Test Info */}
      <div style={{ 
        backgroundColor: '#f8f9fa', 
        padding: '20px', 
        borderRadius: '8px', 
        marginBottom: '20px' 
      }}>
        <h3>{currentTest.name}</h3>
        <p>{currentTest.description}</p>
        <div style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          <div><strong>File URL:</strong> {currentTest.fileUrl}</div>
          <div><strong>File Name:</strong> {currentTest.fileName}</div>
        </div>
      </div>

      {/* Expected Results */}
      <div style={{ 
        backgroundColor: '#fff3cd', 
        border: '1px solid #ffeaa7', 
        padding: '15px', 
        borderRadius: '4px',
        marginBottom: '20px'
      }}>
        <h3>Expected Results:</h3>
        <ul>
          <li><strong>Known Working PDF:</strong> Should show PDF content immediately</li>
          <li><strong>Simple PDF:</strong> Should show PDF content immediately</li>
          <li><strong>Your S3 PDF:</strong> May show CORS error or "PDF Preview Not Available"</li>
          <li><strong>Bad Extension Test:</strong> Should still work (URL detection should catch it)</li>
          <li><strong>No Extension Test:</strong> Should still work (URL detection should catch it)</li>
        </ul>
      </div>

      {/* DocumentViewer Test */}
      <div style={{ 
        border: '2px solid #007bff', 
        padding: '20px', 
        borderRadius: '8px',
        backgroundColor: '#fff'
      }}>
        <h2>DocumentViewer Test</h2>
        <DocumentViewer
          fileUrl={currentTest.fileUrl}
          fileName={currentTest.fileName}
          title={currentTest.name}
          height="600px"
          showDownload={false}
        />
      </div>

      {/* Troubleshooting Guide */}
      <div style={{ 
        backgroundColor: '#f8d7da', 
        border: '1px solid #f5c6cb', 
        padding: '15px', 
        borderRadius: '4px',
        marginTop: '20px'
      }}>
        <h3>Troubleshooting Guide:</h3>
        <ul>
          <li><strong>If Known Working PDF fails:</strong> react-pdf library issue or worker problem</li>
          <li><strong>If Known Working PDF works but S3 fails:</strong> CORS or authentication issue</li>
          <li><strong>If all PDFs show "Only PDF documents are supported":</strong> Document type detection issue</li>
          <li><strong>If PDFs are detected but show "PDF Preview Not Available":</strong> react-pdf rendering issue</li>
        </ul>
      </div>
    </div>
  );
};

export default IsolationTest;
