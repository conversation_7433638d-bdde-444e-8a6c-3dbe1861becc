import React, { useState, useEffect, useCallback } from 'react';
import { FaFilePdf, FaFile, FaDownload, FaExternalLinkAlt, FaExclamationTriangle } from 'react-icons/fa';
import UniversalPDFViewer from './UniversalPDFViewer';
import '../../styles/DocumentViewer.css';

const DocumentViewer = ({
  fileUrl,
  fileName = '',
  title = 'Document',
  className = '',
  height = '400px',
  showDownload = false,
  onDownload = null
}) => {
  const [documentType, setDocumentType] = useState('unknown');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Device detection
  useEffect(() => {
    const userAgent = navigator.userAgent;
    const mobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) ||
      window.innerWidth <= 768;

    setIsMobile(mobileDevice);
  }, []);

  // Get file extension from filename or URL
  const getFileExtension = useCallback((filenameOrUrl) => {
    if (!filenameOrUrl) return '';

    // Try to get extension from filename first
    let source = filenameOrUrl;

    // If it looks like a URL, try to extract the filename from the path
    if (filenameOrUrl.includes('/')) {
      const urlPath = filenameOrUrl.split('?')[0]; // Remove query parameters
      source = urlPath.substring(urlPath.lastIndexOf('/') + 1);
    }

    const lastDotIndex = source.lastIndexOf('.');
    if (lastDotIndex === -1) return '';

    const extension = source.substring(lastDotIndex).toLowerCase();
    console.log('[DocumentViewer] Extension detection:', { filenameOrUrl, source, extension });
    return extension;
  }, []);

  // Enhanced document type detection - focused on PDF files
  const getDocumentType = useCallback(async (filename, previewUrl = null) => {
    console.log('[DocumentViewer] Document type detection:', { filename, previewUrl });

    // First try to get extension from filename
    let extension = getFileExtension(filename);
    console.log('[DocumentViewer] Extension from filename:', extension);

    // If no extension from filename and we have a URL, try to get it from URL
    if (!extension && previewUrl) {
      extension = getFileExtension(previewUrl);
      console.log('[DocumentViewer] Extension from previewUrl:', extension);
    }

    // Extension-based detection - only PDF supported
    if (extension === '.pdf') {
      console.log('[DocumentViewer] Detected as PDF via extension');
      return 'pdf';
    }

    // Check if URL contains 'preview.pdf' or '_preview.pdf' - common S3 pattern
    if (previewUrl && (previewUrl.includes('preview.pdf') || previewUrl.includes('_preview.pdf'))) {
      console.log('[DocumentViewer] Detected as PDF via preview URL pattern');
      return 'pdf';
    }

    // Check if filename contains 'pdf' anywhere (fallback)
    if (filename && filename.toLowerCase().includes('pdf')) {
      console.log('[DocumentViewer] Detected as PDF via filename pattern');
      return 'pdf';
    }

    // Check if URL contains 'pdf' anywhere (fallback)
    if (previewUrl && previewUrl.toLowerCase().includes('pdf')) {
      console.log('[DocumentViewer] Detected as PDF via URL pattern');
      return 'pdf';
    }

    console.log('[DocumentViewer] Could not detect as PDF, marking as unknown');
    return 'unknown';
  }, [getFileExtension]);

  // Initialize document type
  useEffect(() => {
    const initializeDocumentType = async () => {
      if (!fileName && !fileUrl) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const detectedType = await getDocumentType(fileName, fileUrl);
        setDocumentType(detectedType);
      } catch (error) {
        console.error('[DocumentViewer] Document type detection failed:', error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    initializeDocumentType();
  }, [fileName, fileUrl, getDocumentType]);

  // Get icon for document type
  const getDocumentIcon = useCallback((type) => {
    switch (type) {
      case 'pdf':
        return <FaFilePdf className="document-viewer__icon document-viewer__icon--pdf" />;
      default:
        return <FaFile className="document-viewer__icon document-viewer__icon--default" />;
    }
  }, []);

  // Get document type name
  const getDocumentTypeName = useCallback((type) => {
    switch (type) {
      case 'pdf':
        return 'PDF Document';
      default:
        return 'Document';
    }
  }, []);

  // Handle native app opening for mobile
  const handleOpenInNativeApp = useCallback(() => {
    if (!fileUrl) return;

    // Create a download link that will trigger the native app
    const link = document.createElement('a');
    link.href = fileUrl;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';

    // Add download attribute for better mobile support
    if (fileName) {
      link.download = fileName;
    }

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [fileUrl, fileName]);

  // Render based on document type
  const renderViewer = () => {
    switch (documentType) {
      case 'pdf':
        return (
          <UniversalPDFViewer
            fileUrl={fileUrl}
            fileName={fileName}
            title={title}
            className={className}
            height={height}
            showDownload={showDownload}
            onDownload={onDownload}
            showNativeOptions={false} // Force disable native options to always show preview
          />
        );
      default:
        return (
          <div className="document-viewer__unsupported">
            <div className="document-viewer__icon-wrapper">
              {getDocumentIcon(documentType)}
            </div>
            <h3>{getDocumentTypeName(documentType)}</h3>
            <p>Only PDF documents are supported for preview. Please ensure your file is a PDF format.</p>
            {(showDownload || onDownload) && (
              <button
                className="document-viewer__download-btn"
                onClick={handleOpenInNativeApp}
              >
                <FaDownload />
                Download
              </button>
            )}
          </div>
        );
    }
  };

  return (
    <div className={`document-viewer ${className}`} style={{ height }}>
      {isLoading ? (
        <div className="document-viewer__loading">
          <div className="document-viewer__spinner" />
          <p>Loading document...</p>
        </div>
      ) : hasError ? (
        <div className="document-viewer__error">
          <FaExclamationTriangle className="document-viewer__error-icon" />
          <h3>Error Loading Document</h3>
          <p>Unable to load the document preview</p>
          {(showDownload || onDownload) && (
            <button
              className="document-viewer__download-btn"
              onClick={handleOpenInNativeApp}
            >
              <FaDownload />
              Download
            </button>
          )}
        </div>
      ) : (
        renderViewer()
      )}
    </div>
  );
};

export default DocumentViewer;
