import React, { useState, useEffect } from 'react';

const SimpleDetectionTest = () => {
  const [results, setResults] = useState({});

  // Copy the exact functions from DocumentViewer
  const getFileExtension = (filenameOrUrl) => {
    if (!filenameOrUrl) return '';

    let source = filenameOrUrl;

    if (filenameOrUrl.includes('/')) {
      const urlPath = filenameOrUrl.split('?')[0];
      source = urlPath.substring(urlPath.lastIndexOf('/') + 1);
    }

    const lastDotIndex = source.lastIndexOf('.');
    if (lastDotIndex === -1) return '';

    const extension = source.substring(lastDotIndex).toLowerCase();
    console.log('[SimpleTest] Extension detection:', { filenameOrUrl, source, extension });
    return extension;
  };

  const getDocumentType = async (filename, previewUrl = null) => {
    console.log('[SimpleTest] Document type detection:', { filename, previewUrl });

    let extension = getFileExtension(filename);
    console.log('[SimpleTest] Extension from filename:', extension);

    if (!extension && previewUrl) {
      extension = getFileExtension(previewUrl);
      console.log('[SimpleTest] Extension from previewUrl:', extension);
    }

    if (extension === '.pdf') {
      console.log('[SimpleTest] Detected as PDF via extension');
      return 'pdf';
    }

    if (previewUrl && (previewUrl.includes('preview.pdf') || previewUrl.includes('_preview.pdf'))) {
      console.log('[SimpleTest] Detected as PDF via preview URL pattern');
      return 'pdf';
    }

    if (filename && filename.toLowerCase().includes('pdf')) {
      console.log('[SimpleTest] Detected as PDF via filename pattern');
      return 'pdf';
    }

    if (previewUrl && previewUrl.toLowerCase().includes('pdf')) {
      console.log('[SimpleTest] Detected as PDF via URL pattern');
      return 'pdf';
    }

    console.log('[SimpleTest] Could not detect as PDF, marking as unknown');
    return 'unknown';
  };

  useEffect(() => {
    const runTest = async () => {
      const testUrl = "https://xosports.s3.amazonaws.com/previews/1752640818506-Basketball-Coaching-Resource-Book_preview.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3MUHQ5BOLEJMUNN5%2F20250716%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250716T051917Z&X-Amz-Expires=86400&X-Amz-Signature=0bdfa5e180e830073dc84103fcd67859485f04c4c2916c6eb8a4f1b9f3a782d1&X-Amz-SignedHeaders=host";
      const testFileName = "Basketball-Coaching-Resource-Book.pdf";

      const filenameExt = getFileExtension(testFileName);
      const urlExt = getFileExtension(testUrl);
      const detectedType = await getDocumentType(testFileName, testUrl);

      setResults({
        testUrl,
        testFileName,
        filenameExt,
        urlExt,
        detectedType,
        urlContainsPreviewPdf: testUrl.includes('preview.pdf'),
        urlContainsUnderscorePreviewPdf: testUrl.includes('_preview.pdf'),
        filenameContainsPdf: testFileName.toLowerCase().includes('pdf'),
        urlContainsPdf: testUrl.toLowerCase().includes('pdf')
      });
    };

    runTest();
  }, []);

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Simple PDF Detection Test</h1>
      
      {Object.keys(results).length > 0 && (
        <div style={{ backgroundColor: '#f8f9fa', padding: '20px', borderRadius: '8px' }}>
          <h2>Test Results</h2>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <tbody>
              {Object.entries(results).map(([key, value]) => (
                <tr key={key}>
                  <td style={{ 
                    border: '1px solid #ddd', 
                    padding: '8px', 
                    fontWeight: 'bold',
                    backgroundColor: '#e9ecef',
                    width: '30%'
                  }}>
                    {key}
                  </td>
                  <td style={{ 
                    border: '1px solid #ddd', 
                    padding: '8px',
                    wordBreak: 'break-all'
                  }}>
                    {typeof value === 'boolean' ? value.toString() : value}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          <div style={{ marginTop: '20px', padding: '10px', backgroundColor: results.detectedType === 'pdf' ? '#d4edda' : '#f8d7da', borderRadius: '4px' }}>
            <strong>Final Result: </strong>
            {results.detectedType === 'pdf' ? 
              '✅ PDF detected successfully!' : 
              '❌ PDF not detected - this is the problem!'
            }
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleDetectionTest;
